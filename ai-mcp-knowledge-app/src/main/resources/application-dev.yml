server:
  port: 8090

spring:
  main:
    allow-bean-definition-overriding: true
  #  main:
  #    web-application-type: none
  datasource:
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: postgres
    url: ******************************************************
    type: com.zaxxer.hikari.HikariDataSource
  ai:
    mcp:
      client:
        request-timeout: 360s
        stdio:
          servers-configuration: classpath:/config/mcp-servers-config.json
    openai:
      base-url: https://api.zhizengzeng.com/v1
      api-key: sk-zk27131c3a3c96c7bfc2686a6be4178c575689839106ec46
      chat:
        options:
          model: gpt-3.5-turbo
      embedding-model: text-embedding-ada-002
      embedding:
        options:
          num-batch: 1536
    ollama:
      base-url: http://58a39caa684c41bf9bed-deepseek-r1-llm-api.gcs-xy1a.jdcloud.com/
      mode:
      embedding-model: nomic-embed-text
      embedding:
        options:
          num-batch: 768
    dashscope:
      api-key: sk-34063be5822f4e3b897dbdd8370b8314
      chat:
        options:
          model: qwen-turbo

logging:
  level:
    root: info
  config: classpath:logback-spring.xml
