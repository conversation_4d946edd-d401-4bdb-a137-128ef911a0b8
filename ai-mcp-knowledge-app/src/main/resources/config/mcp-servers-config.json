{"mcpServers": {"filesystem": {"command": "E:\\DevSoftware\\NodeJs19\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\Drivers", "D:\\Drivers"]}, "mcp-server-computer": {"command": "D:\\snsoftn10\\SDK\\graalvm-jdk-17.0.9+11.1\\bin\\java.exe", "args": ["-Dfile.encoding=utf-8", "-Dspring.ai.mcp.server.stdio=true", "-jar", "E:\\MavenRepository\\cn\\bugstack\\mcp\\mcp-server-computer\\1.0.0\\mcp-server-computer-1.0.0.jar"]}}}