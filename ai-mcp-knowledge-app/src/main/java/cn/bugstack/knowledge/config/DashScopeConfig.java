package cn.bugstack.knowledge.config;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.DefaultChatClientBuilder;
import org.springframework.ai.chat.client.observation.ChatClientObservationConvention;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DashScopeConfig {

	@Bean("dashScopeChatClientBuilder")
	public ChatClient.Builder dashScopeChatClientBuilder(DashScopeChatModel dashScopeChatModel) {
		return new DefaultChatClientBuilder(dashScopeChatModel, ObservationRegistry.NOOP,
				(ChatClientObservationConvention) null);
	}

}
