package cn.bugstack.knowledge.test;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class MCPTest {

	@Resource
	private ChatClient.Builder chatClientBuilder;

	@Autowired
	private ToolCallbackProvider tools;

	@Test
	public void test22() {
		String userInput = "介绍一下 Spring AI Alibaba";

		var chatClient = chatClientBuilder
				// 确保已注释或移除 tools
				// .defaultTools(tools)
				.defaultOptions(DashScopeChatOptions.builder()
						.withModel("qwen-plus")
						.build())
				.build();

		// 获取完整响应
		ChatClient.CallResponseSpec call = chatClient.prompt(userInput).call();
		System.out.println(call);
		// 打印整个响应结构
		//	log.info("Full ChatResponse: {}", response);
		//	log.info("Response Result: {}", response.getResult());
		//
		//	if (response.getResult() != null) {
		//		var output = response.getResult().getOutput();
		//		log.info("Output: {}", output);
		//		if (output != null) {
		//			log.info("Content: {}", output.getContent());
		//			log.info("Message Type: {}", output.getClass().getSimpleName());
		//			log.info("Properties: {}", output.getProperties());
		//		}
		//	}
		//
		//	// 检查元数据
		//	log.info("Metadata: {}", response.getMetadata());
		//
		//	// 原来的打印
		//	System.out.println("\n>>> ASSISTANT: " + response.getResult().getOutput().getContent());
		//
		//} catch (Exception e) {
		//	log.error("Error during API call", e);
		//	e.printStackTrace();
		//}
	}

	@Test
	public void test_tool() {
		String userInput = "介绍一下 Spring AI Alibaba";
		var chatClient = chatClientBuilder
				.defaultTools(tools)
				.defaultOptions(OpenAiChatOptions.builder()
						.model("gpt-3.5-turbo")
						.build())
				.build();

		System.out.println("\n>>> QUESTION: " + userInput);
		System.out.println("\n>>> ASSISTANT: " + chatClient.prompt(userInput).call().content());
	}

	@Test
	public void test() {
		String userInput = "获取电脑配置";
		//        userInput = "在 /Users/<USER>/Desktop 文件夹下，创建 电脑.txt";
		userInput = "获取电脑配置 在 /Users/<USER>/Desktop 文件夹下，创建 电脑.txt 把电脑配置写入 电脑.txt";

		var chatClient = chatClientBuilder
				.defaultTools(tools)
				.defaultOptions(OpenAiChatOptions.builder()
						.model("gpt-3.5-turbo")
						.build())
				.build();

		System.out.println("\n>>> QUESTION: " + userInput);
		System.out.println("\n>>> ASSISTANT: " + chatClient.prompt(userInput).call().content());
	}

	//@Test
	//public void testAli() {
	//	var chatClient = chatClientBuilder
	//			.defaultTools(tools)
	//			.defaultOptions(DashScopeChatOptions.builder()
	//					.withModel("qwen-plus")
	//					.build())
	//			.build();
	//	// 1. 创建 DashScopeChatOptions 实例并配置
	//	DashScopeChatOptions options = DashScopeChatOptions.builder()
	//			.withModel("qwen-plus") // 指定模型
	//			.withTemperature(0.7)    // 设置温度
	//			.withEnableSearch(true) // 启用搜索 (如果模型支持)
	//			.build();
	//
	//	chatClient.prompt()
	//}

}
