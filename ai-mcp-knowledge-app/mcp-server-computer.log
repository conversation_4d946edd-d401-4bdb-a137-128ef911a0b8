2025-08-20T09:41:54.566+08:00  INFO 34924 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 34924 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T09:41:54.567+08:00  INFO 34924 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T09:41:55.176+08:00  INFO 34924 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T09:41:55.267+08:00  INFO 34924 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T09:41:55.363+08:00  INFO 34924 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.122 seconds (process running for 1.441)
2025-08-20T09:41:55.365+08:00  INFO 34924 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:15:59.471+08:00  INFO 27632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 27632 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T10:15:59.473+08:00  INFO 27632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:16:00.127+08:00  INFO 27632 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:16:00.225+08:00  INFO 27632 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:16:00.314+08:00  INFO 27632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.181 seconds (process running for 1.504)
2025-08-20T10:16:00.317+08:00  INFO 27632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:24:19.398+08:00  INFO 32604 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 32604 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T10:24:19.400+08:00  INFO 32604 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:24:20.028+08:00  INFO 32604 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:24:20.119+08:00  INFO 32604 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:24:20.209+08:00  INFO 32604 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.182 seconds (process running for 1.525)
2025-08-20T10:24:20.211+08:00  INFO 32604 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:24:54.536+08:00  INFO 36764 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 36764 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T10:24:54.538+08:00  INFO 36764 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:24:55.150+08:00  INFO 36764 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:24:55.236+08:00  INFO 36764 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:24:55.325+08:00  INFO 36764 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.126 seconds (process running for 1.447)
2025-08-20T10:24:55.327+08:00  INFO 36764 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:28:15.844+08:00  INFO 33484 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 33484 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T10:28:15.847+08:00  INFO 33484 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:28:16.489+08:00  INFO 33484 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:28:16.608+08:00  INFO 33484 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:28:16.703+08:00  INFO 33484 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.207 seconds (process running for 1.563)
2025-08-20T10:28:16.706+08:00  INFO 33484 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:30:48.833+08:00  INFO 24196 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 24196 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-20T10:30:48.836+08:00  INFO 24196 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:30:49.446+08:00  INFO 24196 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:30:49.536+08:00  INFO 24196 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:30:49.623+08:00  INFO 24196 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.126 seconds (process running for 1.458)
2025-08-20T10:30:49.626+08:00  INFO 24196 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
