2025-08-21T08:21:14.411+08:00  INFO 37028 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 37028 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T08:21:14.419+08:00  INFO 37028 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T08:21:15.091+08:00  INFO 37028 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T08:21:15.186+08:00  INFO 37028 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T08:21:15.278+08:00  INFO 37028 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.288 seconds (process running for 1.643)
2025-08-21T08:21:15.282+08:00  INFO 37028 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T08:21:23.073+08:00  INFO 37028 --- [mcp-server-computer] [boundedElastic-1] c.b.m.s.c.d.service.ComputerService      : 获取电脑配置信息 .
2025-08-21T08:26:03.793+08:00  INFO 34916 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 34916 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T08:26:03.794+08:00  INFO 34916 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T08:26:04.429+08:00  INFO 34916 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T08:26:04.519+08:00  INFO 34916 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T08:26:04.610+08:00  INFO 34916 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.156 seconds (process running for 1.485)
2025-08-21T08:26:04.613+08:00  INFO 34916 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T08:26:13.647+08:00  INFO 34916 --- [mcp-server-computer] [boundedElastic-1] c.b.m.s.c.d.service.ComputerService      : 获取电脑配置信息 .
2025-08-21T08:27:58.236+08:00  INFO 29808 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 29808 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T08:27:58.238+08:00  INFO 29808 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T08:27:58.828+08:00  INFO 29808 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T08:27:58.923+08:00  INFO 29808 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T08:27:59.003+08:00  INFO 29808 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.095 seconds (process running for 1.419)
2025-08-21T08:27:59.006+08:00  INFO 29808 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T08:28:06.883+08:00  INFO 29808 --- [mcp-server-computer] [boundedElastic-1] c.b.m.s.c.d.service.ComputerService      : 获取电脑配置信息 Apple
2025-08-21T08:30:05.718+08:00  INFO 10340 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 10340 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T08:30:05.720+08:00  INFO 10340 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T08:30:06.329+08:00  INFO 10340 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T08:30:06.418+08:00  INFO 10340 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T08:30:06.505+08:00  INFO 10340 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.128 seconds (process running for 1.449)
2025-08-21T08:30:06.508+08:00  INFO 10340 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-21T08:30:17.318+08:00  INFO 10340 --- [mcp-server-computer] [boundedElastic-1] c.b.m.s.c.d.service.ComputerService      : 获取电脑配置信息 Apple
2025-08-21T08:39:31.095+08:00  INFO 31660 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 31660 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge\ai-mcp-knowledge-app)
2025-08-21T08:39:31.097+08:00  INFO 31660 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-21T08:39:31.685+08:00  INFO 31660 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-21T08:39:31.774+08:00  INFO 31660 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-21T08:39:31.862+08:00  INFO 31660 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.096 seconds (process running for 1.407)
2025-08-21T08:39:31.865+08:00  INFO 31660 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
