2025-08-20T08:54:10.679+08:00  INFO 32216 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 32216 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T08:54:10.681+08:00  INFO 32216 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T08:54:11.389+08:00  INFO 32216 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T08:54:11.490+08:00  INFO 32216 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T08:54:11.588+08:00  INFO 32216 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.319 seconds (process running for 1.694)
2025-08-20T08:54:11.590+08:00  INFO 32216 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T08:57:56.843+08:00  INFO 31672 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 31672 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T08:57:56.845+08:00  INFO 31672 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T08:57:57.513+08:00  INFO 31672 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T08:57:57.603+08:00  INFO 31672 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T08:57:57.702+08:00  INFO 31672 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.213 seconds (process running for 1.537)
2025-08-20T08:57:57.705+08:00  INFO 31672 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T09:05:11.530+08:00  INFO 7632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 7632 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T09:05:11.532+08:00  INFO 7632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T09:05:12.125+08:00  INFO 7632 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T09:05:12.214+08:00  INFO 7632 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T09:05:12.297+08:00  INFO 7632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.094 seconds (process running for 1.408)
2025-08-20T09:05:12.300+08:00  INFO 7632 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T09:06:41.569+08:00  INFO 5164 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 5164 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T09:06:41.571+08:00  INFO 5164 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T09:06:42.163+08:00  INFO 5164 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T09:06:42.256+08:00  INFO 5164 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T09:06:42.336+08:00  INFO 5164 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.091 seconds (process running for 1.4)
2025-08-20T09:06:42.338+08:00  INFO 5164 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T09:08:00.101+08:00  INFO 22192 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 22192 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T09:08:00.103+08:00  INFO 22192 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T09:08:00.715+08:00  INFO 22192 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T09:08:00.805+08:00  INFO 22192 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T09:08:00.899+08:00  INFO 22192 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.149 seconds (process running for 1.475)
2025-08-20T09:08:00.903+08:00  INFO 22192 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
2025-08-20T10:49:48.904+08:00  INFO 22884 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Starting McpServerComputerApplication v1.0.0 using Java 17.0.9 with PID 22884 (E:\MavenRepository\cn\bugstack\mcp\mcp-server-computer\1.0.0\mcp-server-computer-1.0.0.jar started by AppleWeb_C in E:\IdeaPro\XFG\ai-mcp-knowledge)
2025-08-20T10:49:48.906+08:00  INFO 22884 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-20T10:49:49.531+08:00  INFO 22884 --- [mcp-server-computer] [main] o.s.a.a.m.s.MpcServerAutoConfiguration   : Registered tools1 notification: true
2025-08-20T10:49:49.631+08:00  INFO 22884 --- [mcp-server-computer] [pool-2-thread-1] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2024-11-05, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=spring-ai-mcp-client, version=1.0.0]
2025-08-20T10:49:49.716+08:00  INFO 22884 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : Started McpServerComputerApplication in 1.148 seconds (process running for 1.493)
2025-08-20T10:49:49.718+08:00  INFO 22884 --- [mcp-server-computer] [main] c.b.m.s.c.McpServerComputerApplication   : mcp server computer success!
