version: '3.8'
# docker-compose -f docker-compose-app-v1.0.yml up -d
services:
  ai-mcp-knowledge-app:
#    image: fuzhengwei/ai-mcp-knowledge-app:1.0
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/ai-mcp-knowledge-app:1.0
    container_name: ai-mcp-knowledge-app
    restart: always
    ports:
      - "8090:8090"
    volumes:
      - ./log:/data/log
      - ./mcp/config:/mcp/config
      - ./mcp/jar:/mcp/jar
    environment:
      - TZ=PRC
      - SERVER_PORT=8090
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATASOURCE_URL=*************************************************
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.postgresql.Driver
      - SPRING_AI_OLLAMA_BASE_URL=http://58a39caa684c41bf9bed-deepseek-r1-llm-api.gcs-xy1a.jdcloud.com/
      - SPRING_AI_OLLAMA_EMBEDDING_OPTIONS_NUM_BATCH=512
      - SPRING_AI_OLLAMA_MODEL=nomic-embed-text
      - SPRING_AI_OPENAI_BASE_URL=https://apis.itedus.cn
      - SPRING_AI_OPENAI_API_KEY=sk-0ppnJfd3Vid8TnMK60D7DdEeA21e420591B343D470190a03
      - SPRING_AI_OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
      - SPRING_AI_RAG_EMBED=nomic-embed-text
      - SPRING_AI_MCP_CLIENT_STDIO_SERVERS_CONFIGURATION=file:/mcp/config/mcp-servers-config.json
      - REDIS_SDK_CONFIG_HOST=redis
      - REDIS_SDK_CONFIG_PORT=6379
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

networks:
  my-network:
    driver: bridge
