version: '3.8'
# docker-compose -f docker-compose-app-v2.0.yml up -d
services:
  ai-mcp-knowledge-app:
#    image: fuzhengwei/ai-mcp-knowledge-app:2.3
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/ai-mcp-knowledge-app:2.3
    container_name: ai-mcp-knowledge-app
    restart: always
    ports:
      - "8090:8090"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8090
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATASOURCE_URL=*****************************************************
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.postgresql.Driver
      - SPRING_AI_OLLAMA_BASE_URL=http://58a39caa684c41bf9bed-deepseek-r1-llm-api.gcs-xy1a.jdcloud.com/
      - SPRING_AI_OLLAMA_EMBEDDING_OPTIONS_NUM_BATCH=512
      - SPRING_AI_OLLAMA_MODEL=nomic-embed-text
      - SPRING_AI_OPENAI_BASE_URL=https://apis.itedus.cn
      - SPRING_AI_OPENAI_API_KEY=sk-BtNEoFEb1xj3jjck4851050468Df4c19916692C955F5C787
      - SPRING_AI_OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
      - SPRING_AI_RAG_EMBED=nomic-embed-text
      - SPRING_AI_MCP_CLIENT_REQUEST_TIMEOUT=360s
      - SPRING_AI_MCP_CLIENT_SSE_CONNECTIONS_MCP_SERVER_CSDN_URL=http://mcp-server-csdn-app:8101
      - SPRING_AI_MCP_CLIENT_SSE_CONNECTIONS_MCP_SERVER_WEIXIN_URL=http://mcp-server-csdn-app:8102
      - REDIS_SDK_CONFIG_HOST=*************
      - REDIS_SDK_CONFIG_PORT=16379
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    depends_on:
      - mcp-server-csdn-app
      - mcp-server-weixin-app
    networks:
      - my-network

  mcp-server-csdn-app:
    #    image: fuzhengwei/mcp-server-csdn-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-csdn-app:1.1
    container_name: mcp-server-csdn-app
    restart: always
    ports:
      - "8101:8101"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8101
      - CSDN_API_CATEGORIES=Java场景面试宝典
      - CSDN_API_COOKIE=uuid_tt_dd=10_37460597350-*************-503302; fid=20_62169880674-*************-403592; c_first_ref=default; c_first_page=https%3A//www.csdn.net/; c_segment=6; dc_sid=f10c7d1fd119eb1bf959a585479b0857; c_ab_test=1; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=**********; HMACCOUNT=E7518BE6E90847F7; loginbox_strategy=%7B%22taskId%22%3A349%2C%22abCheckTime%22%3A**********595%2C%22version%22%3A%22exp11%22%7D; SESSION=beb961db-b099-4263-8cde-5f48147381d7; UserName=weixin_46755643; UserInfo=65db8867619a4646b9ae059b8446c725; UserToken=65db8867619a4646b9ae059b8446c725; UserNick=%E5%B0%8F%E5%82%85%E5%93%A5%E7%9A%84%E7%A0%81%E4%BB%94; AU=1C3; UN=weixin_46755643; BT=*************; p_uid=U010000; csdn_newcert_weixin_46755643=1; c_pref=https%3A//mpbeta.csdn.net/; c_ref=https%3A//www.csdn.net/; dc_session_id=10_1745131415797.140449; c_dsid=11_1745131415934.929779; c_page_id=default; log_Id_pv=1; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=**********; creativeSetApiNew=%7B%22toolbarImg%22%3A%22https%3A//img-home.csdnimg.cn/images/**************.png%22%2C%22publishSuccessImg%22%3A%22https%3A//img-home.csdnimg.cn/images/**************.png%22%2C%22articleNum%22%3A0%2C%22type%22%3A0%2C%22oldUser%22%3Afalse%2C%22useSeven%22%3Atrue%2C%22oldFullVersion%22%3Afalse%2C%22userName%22%3A%22weixin_46755643%22%7D; log_Id_view=22; dc_tos=sv0810
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

  mcp-server-weixin-app:
    #    image: fuzhengwei/  mcp-server-weixin-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-weixin-app:1.1
    container_name: mcp-server-weixin-app
    restart: always
    ports:
      - "8102:8102"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8102
      - WEIXIN_API_ORIGINAL_ID=gh_e067c267e056
      - WEIXIN_API_APP_ID=wx5a228ff69e28a91f
      - WEIXIN_API_APP_SECRET=0bea03aa1310bac050aae79dd8703928
      - WEIXIN_API_TEMPLATE_ID=O8qI6gy75F-bXfPiQugInTMLA0MRzaMff9WSBb16cFk
      - WEIXIN_API_TOUSER=or0Ab6ivwmypESVp_bYuk92T6SvU
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

networks:
  my-network:
    driver: bridge
