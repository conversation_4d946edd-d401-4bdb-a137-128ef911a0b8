CREATE EXTENSION IF NOT EXISTS vector;

-- 查询表；SELECT * FROM information_schema.tables

-- 删除旧的表（如果存在）
DROP TABLE IF EXISTS public.vector_store_openai;

-- 创建新的表，使用UUID作为主键
CREATE TABLE public.vector_store_openai (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    metadata JSONB,
    embedding VECTOR(1536)
);

-- 删除旧的表（如果存在）
DROP TABLE IF EXISTS public.vector_store_ollama_deepseek;

-- 创建新的表，使用UUID作为主键
CREATE TABLE public.vector_store_ollama_deepseek (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    metadata JSONB,
    embedding VECTOR(768)
);
